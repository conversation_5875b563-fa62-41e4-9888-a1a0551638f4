# Generated by Django 5.2.7 on 2025-10-06 22:06

import django.db.models.expressions
import django.db.models.fields.json
import django.db.models.functions.comparison
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='product_price',
            field=models.GeneratedField(db_persist=True, expression=django.db.models.expressions.CombinedExpression(django.db.models.functions.comparison.Cast(models.F('product_data__productInfo__price'), models.PositiveIntegerField()), '/', models.Value(100)), output_field=models.DecimalField(decimal_places=2, max_digits=9), verbose_name='合作达人数'),
        ),
        migrations.AddField(
            model_name='product',
            name='product_title',
            field=models.GeneratedField(db_persist=True, expression=django.db.models.fields.json.KeyTextTransform('productInfo__title', 'product_data'), output_field=models.CharField(max_length=1024), verbose_name='商品名称'),
        ),
    ]
