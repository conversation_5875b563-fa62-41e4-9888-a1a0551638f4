# Generated by Django 5.2.7 on 2025-10-06 23:13

import django.db.models.expressions
import django.db.models.fields.json
import django.db.models.functions.comparison
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0003_alter_product_product_title'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='commission_amount',
            field=models.GeneratedField(db_persist=True, expression=django.db.models.expressions.CombinedExpression(django.db.models.functions.comparison.Cast(django.db.models.fields.json.KeyTextTransform('amount', django.db.models.fields.json.KeyTextTransform('commissionInfo', 'product_data')), models.DecimalField(decimal_places=2, max_digits=9)), '/', models.Value(100)), output_field=models.DecimalField(decimal_places=2, max_digits=9), verbose_name='佣金金额'),
        ),
        migrations.AddField(
            model_name='product',
            name='commission_ratio',
            field=models.GeneratedField(db_persist=True, expression=django.db.models.functions.comparison.Cast(django.db.models.fields.json.KeyTextTransform('ratio', django.db.models.fields.json.KeyTextTransform('commissionInfo', 'product_data')), models.DecimalField(decimal_places=2, max_digits=5)), output_field=models.DecimalField(decimal_places=2, max_digits=5), verbose_name='佣金比例(%)'),
        ),
        migrations.AddField(
            model_name='product',
            name='is_commission_hidden',
            field=models.GeneratedField(db_persist=True, expression=django.db.models.functions.comparison.Cast(django.db.models.fields.json.KeyTextTransform('isHidden', django.db.models.fields.json.KeyTextTransform('commissionInfo', 'product_data')), models.BooleanField()), output_field=models.BooleanField(), verbose_name='佣金是否隐藏'),
        ),
        migrations.AddField(
            model_name='product',
            name='product_id',
            field=models.GeneratedField(db_persist=True, expression=django.db.models.fields.json.KeyTextTransform('id', 'product_data'), output_field=models.CharField(max_length=100), verbose_name='商品ID'),
        ),
        migrations.AddField(
            model_name='product',
            name='product_img_url',
            field=models.GeneratedField(db_persist=True, expression=django.db.models.fields.json.KeyTextTransform('imgUrl', django.db.models.fields.json.KeyTextTransform('productInfo', 'product_data')), output_field=models.URLField(max_length=1024), verbose_name='商品图片'),
        ),
        migrations.AddField(
            model_name='product',
            name='product_status',
            field=models.GeneratedField(db_persist=True, expression=django.db.models.functions.comparison.Cast(django.db.models.fields.json.KeyTextTransform('status', 'product_data'), models.IntegerField()), output_field=models.IntegerField(), verbose_name='商品状态'),
        ),
        migrations.AddField(
            model_name='product',
            name='promotion_end_time',
            field=models.GeneratedField(db_persist=True, expression=django.db.models.functions.comparison.Cast(django.db.models.fields.json.KeyTextTransform('endTime', django.db.models.fields.json.KeyTextTransform('commissionInfo', 'product_data')), models.BigIntegerField()), output_field=models.BigIntegerField(), verbose_name='推广结束时间'),
        ),
        migrations.AddField(
            model_name='product',
            name='promotion_start_time',
            field=models.GeneratedField(db_persist=True, expression=django.db.models.functions.comparison.Cast(django.db.models.fields.json.KeyTextTransform('startTime', django.db.models.fields.json.KeyTextTransform('commissionInfo', 'product_data')), models.BigIntegerField()), output_field=models.BigIntegerField(), verbose_name='推广开始时间'),
        ),
        migrations.AddField(
            model_name='product',
            name='service_amount',
            field=models.GeneratedField(db_persist=True, expression=django.db.models.expressions.CombinedExpression(django.db.models.functions.comparison.Cast(django.db.models.fields.json.KeyTextTransform('serviceAmount', django.db.models.fields.json.KeyTextTransform('commissionInfo', 'product_data')), models.DecimalField(decimal_places=2, max_digits=9)), '/', models.Value(100)), output_field=models.DecimalField(decimal_places=2, max_digits=9), verbose_name='服务费金额'),
        ),
        migrations.AddField(
            model_name='product',
            name='service_ratio',
            field=models.GeneratedField(db_persist=True, expression=django.db.models.functions.comparison.Cast(django.db.models.fields.json.KeyTextTransform('serviceRatio', django.db.models.fields.json.KeyTextTransform('commissionInfo', 'product_data')), models.DecimalField(decimal_places=2, max_digits=5)), output_field=models.DecimalField(decimal_places=2, max_digits=5), verbose_name='服务费比例(%)'),
        ),
        migrations.AddField(
            model_name='product',
            name='shop_head_img',
            field=models.GeneratedField(db_persist=True, expression=django.db.models.fields.json.KeyTextTransform('headImg', django.db.models.fields.json.KeyTextTransform('shopInfo', 'product_data')), output_field=models.URLField(max_length=1024), verbose_name='店铺头像'),
        ),
        migrations.AddField(
            model_name='product',
            name='shop_name',
            field=models.GeneratedField(db_persist=True, expression=django.db.models.fields.json.KeyTextTransform('name', django.db.models.fields.json.KeyTextTransform('shopInfo', 'product_data')), output_field=models.CharField(max_length=255), verbose_name='店铺名称'),
        ),
        migrations.AddField(
            model_name='product',
            name='spu_id',
            field=models.GeneratedField(db_persist=True, expression=django.db.models.fields.json.KeyTextTransform('spuId', 'product_data'), output_field=models.CharField(max_length=100), verbose_name='SPU ID'),
        ),
        migrations.AddField(
            model_name='product',
            name='stock_num',
            field=models.GeneratedField(db_persist=True, expression=django.db.models.functions.comparison.Cast(django.db.models.fields.json.KeyTextTransform('stockNum', django.db.models.fields.json.KeyTextTransform('productInfo', 'product_data')), models.IntegerField()), output_field=models.IntegerField(), verbose_name='库存数量'),
        ),
    ]
