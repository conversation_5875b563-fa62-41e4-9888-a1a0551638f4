# Generated by Django 5.2.7 on 2025-10-06 23:28

import django.db.models.expressions
import django.db.models.fields.json
import django.db.models.functions.comparison
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0004_product_commission_amount_product_commission_ratio_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='commission_type',
            field=models.GeneratedField(db_persist=True, expression=django.db.models.functions.comparison.Cast(django.db.models.fields.json.KeyTextTransform('commissionType', django.db.models.fields.json.KeyTextTransform('commissionInfo', 'product_data')), models.IntegerField()), output_field=models.IntegerField(), verbose_name='佣金类型'),
        ),
        migrations.AddField(
            model_name='product',
            name='item_link',
            field=models.GeneratedField(db_persist=True, expression=django.db.models.fields.json.KeyTextTransform('itemLink', django.db.models.fields.json.KeyTextTransform('itemInfo', 'product_data')), output_field=models.CharField(max_length=500), verbose_name='商品链接'),
        ),
        migrations.AddField(
            model_name='product',
            name='normal_second_service_amount',
            field=models.GeneratedField(db_persist=True, expression=django.db.models.expressions.CombinedExpression(django.db.models.functions.comparison.Cast(django.db.models.fields.json.KeyTextTransform('normalSecondServiceAmount', django.db.models.fields.json.KeyTextTransform('commissionInfo', 'product_data')), models.DecimalField(decimal_places=2, max_digits=9)), '/', models.Value(100)), output_field=models.DecimalField(decimal_places=2, max_digits=9), verbose_name='普通二级服务费金额'),
        ),
        migrations.AddField(
            model_name='product',
            name='product_type',
            field=models.GeneratedField(db_persist=True, expression=django.db.models.functions.comparison.Cast(django.db.models.fields.json.KeyTextTransform('type', 'product_data'), models.IntegerField()), output_field=models.IntegerField(), verbose_name='商品类型'),
        ),
        migrations.AddField(
            model_name='product',
            name='second_service_amount',
            field=models.GeneratedField(db_persist=True, expression=django.db.models.expressions.CombinedExpression(django.db.models.functions.comparison.Cast(django.db.models.fields.json.KeyTextTransform('secondServiceAmount', django.db.models.fields.json.KeyTextTransform('commissionInfo', 'product_data')), models.DecimalField(decimal_places=2, max_digits=9)), '/', models.Value(100)), output_field=models.DecimalField(decimal_places=2, max_digits=9), verbose_name='二级服务费金额'),
        ),
        migrations.AddField(
            model_name='product',
            name='source_name',
            field=models.GeneratedField(db_persist=True, expression=django.db.models.fields.json.KeyTextTransform('sourceName', django.db.models.fields.json.KeyTextTransform('itemInfo', 'product_data')), output_field=models.CharField(max_length=255), verbose_name='来源名称'),
        ),
    ]
