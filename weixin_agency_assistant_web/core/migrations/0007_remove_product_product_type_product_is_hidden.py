# Generated by Django 5.2.7 on 2025-10-07 00:02

import django.db.models.fields.json
import django.db.models.functions.comparison
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0006_remove_product_is_commission_hidden'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='product',
            name='product_type',
        ),
        migrations.AddField(
            model_name='product',
            name='is_hidden',
            field=models.GeneratedField(db_persist=True, expression=django.db.models.functions.comparison.Cast(django.db.models.fields.json.KeyTextTransform('isHidden', django.db.models.fields.json.KeyTextTransform('commissionInfo', 'product_data')), models.BooleanField()), output_field=models.BooleanField(), verbose_name='是否隐藏'),
        ),
    ]
