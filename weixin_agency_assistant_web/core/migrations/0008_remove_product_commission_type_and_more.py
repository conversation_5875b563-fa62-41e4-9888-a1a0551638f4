# Generated by Django 5.2.7 on 2025-10-07 07:34

import django.db.models.fields.json
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0007_remove_product_product_type_product_is_hidden'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='product',
            name='commission_type',
        ),
        migrations.RemoveField(
            model_name='product',
            name='normal_second_service_amount',
        ),
        migrations.RemoveField(
            model_name='product',
            name='second_service_amount',
        ),
        migrations.AlterField(
            model_name='product',
            name='spu_id',
            field=models.GeneratedField(db_persist=True, expression=django.db.models.fields.json.KeyTextTransform('spuId', 'product_data'), output_field=models.CharField(max_length=100), verbose_name='商品编码'),
        ),
    ]
