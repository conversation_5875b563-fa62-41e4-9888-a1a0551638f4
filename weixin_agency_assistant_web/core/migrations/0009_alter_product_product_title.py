# Generated by Django 5.2.7 on 2025-10-07 07:45

import django.db.models.fields.json
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0008_remove_product_commission_type_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='product',
            name='product_title',
            field=models.GeneratedField(db_persist=True, expression=django.db.models.fields.json.KeyTextTransform('title', django.db.models.fields.json.KeyTextTransform('productInfo', 'product_data')), output_field=models.Char<PERSON>ield(max_length=1024), verbose_name='商品名称'),
        ),
    ]
