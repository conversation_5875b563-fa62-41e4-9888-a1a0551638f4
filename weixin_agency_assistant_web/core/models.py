from django.core.serializers.json import DjangoJSONEncoder
from django.db import models
from django.db.models import F, DecimalField
from django.db.models.fields.json import KeyTextTransform
from django.db.models.functions import Cast


# Create your models here.

class Product(models.Model):
    class Meta:
        verbose_name = '商品'
        verbose_name_plural = '商品'

    product_data = models.JSONField(
        null=True,
        blank=True,
        default=dict,
        encoder=DjangoJSONEncoder,
    )

    product_title = models.GeneratedField(
        expression=KeyTextTransform(
            'title',
            KeyTextTransform('productInfo', 'product_data')
        ),
        output_field=models.CharField(max_length=1024),
        db_persist=True,
        verbose_name='商品名称',
        # unique=True,
    )

    product_price = models.GeneratedField(
        expression=Cast(
            KeyTextTransform('price', KeyTextTransform('productInfo', 'product_data')),
            DecimalField(max_digits=9, decimal_places=2)
        ) / 100,
        output_field=DecimalField(max_digits=9, decimal_places=2),
        db_persist=True,
        verbose_name='商品价格'
    )

    # 商品图片URL
    product_img_url = models.GeneratedField(
        expression=KeyTextTransform(
            'imgUrl',
            KeyTextTransform('productInfo', 'product_data')
        ),
        output_field=models.URLField(max_length=1024),
        db_persist=True,
        verbose_name='商品图片'
    )

    # 库存数量
    stock_num = models.GeneratedField(
        expression=Cast(
            KeyTextTransform('stockNum', KeyTextTransform('productInfo', 'product_data')),
            models.IntegerField()
        ),
        output_field=models.IntegerField(),
        db_persist=True,
        verbose_name='库存数量'
    )

    # 佣金比例
    commission_ratio = models.GeneratedField(
        expression=Cast(
            KeyTextTransform('ratio', KeyTextTransform('commissionInfo', 'product_data')),
            DecimalField(max_digits=5, decimal_places=2)
        ),
        output_field=DecimalField(max_digits=5, decimal_places=2),
        db_persist=True,
        verbose_name='佣金比例(%)'
    )

    # 服务费比例
    service_ratio = models.GeneratedField(
        expression=Cast(
            KeyTextTransform('serviceRatio', KeyTextTransform('commissionInfo', 'product_data')),
            DecimalField(max_digits=5, decimal_places=2)
        ),
        output_field=DecimalField(max_digits=5, decimal_places=2),
        db_persist=True,
        verbose_name='服务费比例(%)'
    )

    # 佣金金额
    commission_amount = models.GeneratedField(
        expression=Cast(
            KeyTextTransform('amount', KeyTextTransform('commissionInfo', 'product_data')),
            DecimalField(max_digits=9, decimal_places=2)
        ) / 100,
        output_field=DecimalField(max_digits=9, decimal_places=2),
        db_persist=True,
        verbose_name='佣金金额'
    )

    # 服务费金额
    service_amount = models.GeneratedField(
        expression=Cast(
            KeyTextTransform('serviceAmount', KeyTextTransform('commissionInfo', 'product_data')),
            DecimalField(max_digits=9, decimal_places=2)
        ) / 100,
        output_field=DecimalField(max_digits=9, decimal_places=2),
        db_persist=True,
        verbose_name='服务费金额'
    )

    # 商品状态
    product_status = models.GeneratedField(
        expression=Cast(
            KeyTextTransform('status', 'product_data'),
            models.IntegerField()
        ),
        output_field=models.IntegerField(),
        db_persist=True,
        verbose_name='商品状态'
    )

    # 商品ID
    product_id = models.GeneratedField(
        expression=KeyTextTransform('id', 'product_data'),
        output_field=models.CharField(max_length=100),
        db_persist=True,
        verbose_name='商品ID'
    )

    # SPU ID
    spu_id = models.GeneratedField(
        expression=KeyTextTransform('spuId', 'product_data'),
        output_field=models.CharField(max_length=100),
        db_persist=True,
        verbose_name='商品编码'
    )

    # 店铺名称
    shop_name = models.GeneratedField(
        expression=KeyTextTransform(
            'name',
            KeyTextTransform('shopInfo', 'product_data')
        ),
        output_field=models.CharField(max_length=255),
        db_persist=True,
        verbose_name='店铺名称'
    )

    # 店铺头像
    shop_head_img = models.GeneratedField(
        expression=KeyTextTransform(
            'headImg',
            KeyTextTransform('shopInfo', 'product_data')
        ),
        output_field=models.URLField(max_length=1024),
        db_persist=True,
        verbose_name='店铺头像'
    )

    # 推广开始时间
    promotion_start_time = models.GeneratedField(
        expression=Cast(
            KeyTextTransform('startTime', KeyTextTransform('commissionInfo', 'product_data')),
            models.BigIntegerField()
        ),
        output_field=models.BigIntegerField(),
        db_persist=True,
        verbose_name='推广开始时间'
    )

    # 推广结束时间
    promotion_end_time = models.GeneratedField(
        expression=Cast(
            KeyTextTransform('endTime', KeyTextTransform('commissionInfo', 'product_data')),
            models.BigIntegerField()
        ),
        output_field=models.BigIntegerField(),
        db_persist=True,
        verbose_name='推广结束时间'
    )

    # 来源名称
    source_name = models.GeneratedField(
        expression=KeyTextTransform(
            'sourceName',
            KeyTextTransform('itemInfo', 'product_data')
        ),
        output_field=models.CharField(max_length=255),
        db_persist=True,
        verbose_name='来源名称'
    )

    # 商品链接
    item_link = models.GeneratedField(
        expression=KeyTextTransform(
            'itemLink',
            KeyTextTransform('itemInfo', 'product_data')
        ),
        output_field=models.CharField(max_length=500),
        db_persist=True,
        verbose_name='商品链接'
    )

    # 是否隐藏
    is_hidden = models.GeneratedField(
        expression=Cast(
            KeyTextTransform('isHidden', KeyTextTransform('commissionInfo', 'product_data')),
            models.BooleanField()
        ),
        output_field=models.BooleanField(),
        db_persist=True,
        verbose_name='是否隐藏'
    )

    def __str__(self):
        return self.product_title
