{% load static %}

<style>
.hover-tip-container {
    position: relative;
    display: inline-block;
    cursor: pointer;
}

.hover-tip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.hover-tip::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #333;
}

.hover-tip-container:hover .hover-tip {
    opacity: 1;
    visibility: visible;
}

.restriction-status {
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

.restriction-none {
    color: #28a745;
    background-color: #d4edda;
}

.restriction-limited {
    color: #dc3545;
    background-color: #f8d7da;
}

.restriction-item {
    margin-bottom: 8px;
    border: 1px solid #555;
    border-radius: 4px;
    overflow: hidden;
}

.restriction-item:last-child {
    margin-bottom: 0;
}

.restriction-title {
    background-color: #444;
    padding: 6px 8px;
    cursor: pointer;
    font-weight: bold;
    font-size: 11px;
    user-select: none;
    transition: background-color 0.2s;
}

.restriction-title:hover {
    background-color: #555;
}

.restriction-title::before {
    content: "▶ ";
    transition: transform 0.2s;
    display: inline-block;
}

.restriction-title.expanded::before {
    transform: rotate(90deg);
}

.restriction-content {
    background-color: #222;
    font-size: 11px;
    line-height: 1.3;
    max-height: 0;
    padding: 0;
    overflow: hidden;
    transition: max-height 0.15s ease-out, padding 0.15s ease-out;
    border-top: none;
}

.restriction-content.expanded {
    max-height: 200px;
    padding: 6px 8px;
    border-top: 1px solid #555;
}
</style>

<div class="hover-tip-container">
    {% if has_restriction %}
        <span class="restriction-status restriction-limited">✗ 有限制</span>
        <div class="hover-tip">
            {% if restriction_items %}
                {% for item in restriction_items %}
                    <div class="restriction-item">
                        <div class="restriction-title" onclick="toggleRestriction(this)">
                            {{ item.title|default:"限制项目" }}
                        </div>
                        <div class="restriction-content">
                            {{ item.wording|default:"无详细说明" }}
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                未知限制原因
            {% endif %}
        </div>
    {% else %}
        <span class="restriction-status restriction-none">✓ 无限制</span>
    {% endif %}
</div>

<script>
function toggleRestriction(element) {
    const content = element.nextElementSibling;
    const isExpanded = element.classList.contains('expanded');

    if (isExpanded) {
        element.classList.remove('expanded');
        content.classList.remove('expanded');
    } else {
        element.classList.add('expanded');
        content.classList.add('expanded');
    }
}
</script>
