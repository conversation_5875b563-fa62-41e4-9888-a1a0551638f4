{% load static %}

<style>
.promotion-link-container {
    position: relative;
    display: inline-block;
}

.promotion-link-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.promotion-link-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    color: white;
    text-decoration: none;
}

.promotion-link-btn:active {
    transform: translateY(0);
}

.promotion-link-icon {
    width: 14px;
    height: 14px;
}

/* 弹窗样式 */
.promotion-modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(4px);
}

.promotion-modal-content {
    background-color: #fefefe;
    margin: 10% auto;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.promotion-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.promotion-modal-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.promotion-modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.promotion-modal-close:hover {
    background-color: rgba(255,255,255,0.2);
}

.promotion-modal-body {
    padding: 30px;
}

.promotion-section {
    margin-bottom: 25px;
}

.promotion-section:last-child {
    margin-bottom: 0;
}

.promotion-section-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.promotion-section-icon {
    width: 16px;
    height: 16px;
    color: #667eea;
}

.promotion-link-input {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 13px;
    font-family: 'Courier New', monospace;
    background-color: #f8f9fa;
    color: #495057;
    resize: none;
    transition: border-color 0.2s;
}

.promotion-link-input:focus {
    outline: none;
    border-color: #667eea;
    background-color: white;
}

.promotion-qr-container {
    text-align: center;
}

.promotion-qr-code {
    margin: 0 auto 15px;
    padding: 15px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    display: inline-block;
}

.promotion-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 20px;
}

.promotion-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 6px;
}

.promotion-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.promotion-btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.promotion-btn-secondary {
    background: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;
}

.promotion-btn-secondary:hover {
    background: #e9ecef;
    color: #495057;
}

.promotion-btn-icon {
    width: 14px;
    height: 14px;
}

.copy-success {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    font-size: 14px;
    z-index: 10001;
    animation: copySuccessSlide 3s ease-out forwards;
}

@keyframes copySuccessSlide {
    0% {
        opacity: 0;
        transform: translateX(100%);
    }
    10%, 90% {
        opacity: 1;
        transform: translateX(0);
    }
    100% {
        opacity: 0;
        transform: translateX(100%);
    }
}
</style>

<div class="promotion-link-container">
    <a href="#" class="promotion-link-btn" onclick="openPromotionModal('{{ promotion_link|escapejs }}', '{{ product_title|escapejs }}'); return false;">
        <svg class="promotion-link-icon" fill="currentColor" viewBox="0 0 20 20">
            <path d="M12.586 4.586a2 2 0 112.828 2.828l-3 3a2 2 0 01-2.828 0 1 1 0 00-1.414 1.414 4 4 0 005.656 0l3-3a4 4 0 00-5.656-5.656l-1.5 1.5a1 1 0 101.414 1.414l1.5-1.5z"/>
            <path d="M7.414 15.414a2 2 0 01-2.828-2.828l3-3a2 2 0 012.828 0 1 1 0 001.414-1.414 4 4 0 00-5.656 0l-3 3a4 4 0 105.656 5.656l1.5-1.5a1 1 0 10-1.414-1.414l-1.5 1.5z"/>
        </svg>
        推广链接
    </a>
</div>

<!-- 推广链接弹窗 -->
<div id="promotionModal" class="promotion-modal">
    <div class="promotion-modal-content">
        <div class="promotion-modal-header">
            <h3 class="promotion-modal-title">推广链接与二维码</h3>
            <button class="promotion-modal-close" onclick="closePromotionModal()">&times;</button>
        </div>
        <div class="promotion-modal-body">
            <div class="promotion-section">
                <div class="promotion-section-title">
                    <svg class="promotion-section-icon" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M12.586 4.586a2 2 0 112.828 2.828l-3 3a2 2 0 01-2.828 0 1 1 0 00-1.414 1.414 4 4 0 005.656 0l3-3a4 4 0 00-5.656-5.656l-1.5 1.5a1 1 0 101.414 1.414l1.5-1.5z"/>
                        <path d="M7.414 15.414a2 2 0 01-2.828-2.828l3-3a2 2 0 012.828 0 1 1 0 001.414-1.414 4 4 0 00-5.656 0l-3 3a4 4 0 105.656 5.656l1.5-1.5a1 1 0 10-1.414-1.414l-1.5 1.5z"/>
                    </svg>
                    推广链接
                </div>
                <textarea id="promotionLinkText" class="promotion-link-input" rows="3" readonly></textarea>
            </div>
            
            <div class="promotion-section">
                <div class="promotion-section-title">
                    <svg class="promotion-section-icon" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm2 2V5h1v1H5zM3 13a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1H4a1 1 0 01-1-1v-3zm2 2v-1h1v1H5zM13 3a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1V4a1 1 0 011-1h3zm-1 2v1h-1V5h1zM11 13a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-3zm2 2v-1h1v1h-1z"/>
                    </svg>
                    推广二维码
                </div>
                <div class="promotion-qr-container">
                    <div id="promotionQRCode" class="promotion-qr-code"></div>
                    <div class="promotion-actions">
                        <button class="promotion-btn promotion-btn-primary" onclick="copyPromotionLink()">
                            <svg class="promotion-btn-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"/>
                                <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"/>
                            </svg>
                            复制链接
                        </button>
                        <button class="promotion-btn promotion-btn-secondary" onclick="downloadQRCode()">
                            <svg class="promotion-btn-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"/>
                            </svg>
                            下载二维码
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="{% static 'js/qrcode.min.js' %}"></script>
<script>
let currentQRCode = null;
let currentPromotionLink = '';
let currentProductTitle = '';

function openPromotionModal(promotionLink, productTitle) {
    currentPromotionLink = promotionLink;
    currentProductTitle = productTitle;
    
    // 设置链接文本
    document.getElementById('promotionLinkText').value = promotionLink;
    
    // 清除之前的二维码
    const qrContainer = document.getElementById('promotionQRCode');
    qrContainer.innerHTML = '';
    
    // 生成新的二维码
    if (promotionLink) {
        currentQRCode = new QRCode(qrContainer, {
            text: promotionLink,
            width: 200,
            height: 200,
            colorDark: "#000000",
            colorLight: "#ffffff",
            correctLevel: QRCode.CorrectLevel.M
        });
    }
    
    // 显示弹窗
    document.getElementById('promotionModal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function closePromotionModal() {
    document.getElementById('promotionModal').style.display = 'none';
    document.body.style.overflow = 'auto';
}

function copyPromotionLink() {
    const linkText = document.getElementById('promotionLinkText');
    linkText.select();
    linkText.setSelectionRange(0, 99999); // For mobile devices
    
    try {
        document.execCommand('copy');
        showCopySuccess();
    } catch (err) {
        // Fallback for modern browsers
        navigator.clipboard.writeText(currentPromotionLink).then(() => {
            showCopySuccess();
        }).catch(() => {
            alert('复制失败，请手动复制链接');
        });
    }
}

function showCopySuccess() {
    const successDiv = document.createElement('div');
    successDiv.className = 'copy-success';
    successDiv.textContent = '✓ 推广链接已复制到剪贴板';
    document.body.appendChild(successDiv);
    
    setTimeout(() => {
        document.body.removeChild(successDiv);
    }, 3000);
}

function downloadQRCode() {
    if (!currentQRCode) return;
    
    const canvas = document.querySelector('#promotionQRCode canvas');
    if (canvas) {
        const link = document.createElement('a');
        link.download = `推广二维码-${currentProductTitle || '商品'}.png`;
        link.href = canvas.toDataURL();
        link.click();
    }
}

// 点击弹窗外部关闭
window.onclick = function(event) {
    const modal = document.getElementById('promotionModal');
    if (event.target === modal) {
        closePromotionModal();
    }
}

// ESC键关闭弹窗
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closePromotionModal();
    }
});
</script>
