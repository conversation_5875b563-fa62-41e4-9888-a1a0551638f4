{% load static %}

<style>
.promotion-container {
    position: relative;
    display: inline-block;
}

.promotion-link {
    background: #007cba;
    color: white;
    padding: 4px 8px;
    border-radius: 3px;
    text-decoration: none;
    font-size: 12px;
    cursor: pointer;
}

.promotion-link:hover {
    background: #005a87;
    color: white;
    text-decoration: none;
}

.promotion-popup {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    z-index: 1000;
    width: 200px;
}

.promotion-popup.popup-up {
    top: auto;
    bottom: 100%;
    margin-bottom: 5px;
}

.promotion-container:hover .promotion-popup {
    display: block;
}

.qr-code {
    text-align: center;
    margin-bottom: 10px;
}

.link-text {
    font-size: 11px;
    word-break: break-all;
    color: #666;
    margin-top: 5px;
}
</style>

<div class="promotion-container" data-link="{{ promotion_link|escapejs }}">
    <a href="{{ promotion_link }}" target="_blank" class="promotion-link">🔗 推广链接</a>

    <div class="promotion-popup">
        <div class="qr-code"></div>
        <div class="link-text">{{ promotion_link|truncatechars:50 }}</div>
    </div>
</div>

<script src="{% static 'js/qrcode.min.js' %}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 为每个推广容器生成二维码
    document.querySelectorAll('.promotion-container').forEach(function(container) {
        const qrContainer = container.querySelector('.qr-code');
        const link = container.getAttribute('data-link');

        if (qrContainer && link && !qrContainer.hasChildNodes()) {
            new QRCode(qrContainer, {
                text: link,
                width: 100,
                height: 100
            });
        }

        // 添加悬停事件来检测位置
        container.addEventListener('mouseenter', function() {
            const popup = container.querySelector('.promotion-popup');
            const rect = container.getBoundingClientRect();
            const popupHeight = 150; // 大概的popup高度
            const windowHeight = window.innerHeight;

            // 如果popup会超出底部边界，则向上显示
            if (rect.bottom + popupHeight > windowHeight) {
                popup.classList.add('popup-up');
            } else {
                popup.classList.remove('popup-up');
            }
        });
    });
});
</script>
