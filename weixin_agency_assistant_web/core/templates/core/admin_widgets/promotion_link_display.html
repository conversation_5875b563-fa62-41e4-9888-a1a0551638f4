{% load static %}

<style>
.promotion-container {
    position: relative;
    display: inline-block;
}

.promotion-link {
    background: #007cba;
    color: white;
    padding: 4px 8px;
    border-radius: 3px;
    text-decoration: none;
    font-size: 12px;
    cursor: pointer;
}

.promotion-link:hover {
    background: #005a87;
    color: white;
    text-decoration: none;
}

.promotion-popup {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    z-index: 1000;
    width: 200px;
}

.promotion-popup.popup-up {
    top: auto;
    bottom: 100%;
    margin-bottom: 5px;
}

.promotion-popup.show {
    display: block;
}

.qr-code {
    text-align: center;
    margin-bottom: 10px;
}

.link-text {
    font-size: 11px;
    word-break: break-all;
    color: #666;
    margin-top: 5px;
}
</style>

<div class="promotion-container" data-link="{{ promotion_link|escapejs }}">
    <a href="#" class="promotion-link" onclick="return false;">🔗 推广链接</a>

    <div class="promotion-popup">
        <div class="qr-code"></div>
        <div class="link-text">{{ promotion_link|truncatechars:50 }}</div>
    </div>
</div>

<script src="{% static 'js/qrcode.min.js' %}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 为每个推广容器生成二维码
    document.querySelectorAll('.promotion-container').forEach(function(container) {
        const qrContainer = container.querySelector('.qr-code');
        const link = container.getAttribute('data-link');

        if (qrContainer && link && !qrContainer.hasChildNodes()) {
            new QRCode(qrContainer, {
                text: link,
                width: 100,
                height: 100
            });
        }

        // 添加点击事件
        const linkBtn = container.querySelector('.promotion-link');
        linkBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const popup = container.querySelector('.promotion-popup');

            // 关闭其他所有popup
            document.querySelectorAll('.promotion-popup.show').forEach(function(otherPopup) {
                if (otherPopup !== popup) {
                    otherPopup.classList.remove('show');
                }
            });

            // 检测位置
            const rect = container.getBoundingClientRect();
            const popupHeight = 150;
            const windowHeight = window.innerHeight;

            if (rect.bottom + popupHeight > windowHeight) {
                popup.classList.add('popup-up');
            } else {
                popup.classList.remove('popup-up');
            }

            // 切换当前popup显示状态
            popup.classList.toggle('show');
        });
    });

    // 点击其他地方关闭popup
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.promotion-container')) {
            document.querySelectorAll('.promotion-popup.show').forEach(function(popup) {
                popup.classList.remove('show');
            });
        }
    });
});
</script>
