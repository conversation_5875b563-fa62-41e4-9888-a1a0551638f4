{% load static %}

<style>
.promotion-link-container {
    position: relative;
    display: inline-block;
}

.promotion-link-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.promotion-link-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    color: white;
    text-decoration: none;
}

.promotion-link-btn:active {
    transform: translateY(0);
}

.promotion-link-btn.active {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

.promotion-link-icon {
    width: 14px;
    height: 14px;
}

/* 悬浮提示框样式 */
.promotion-popup {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: white;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    padding: 20px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s, transform 0.3s;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    width: 320px;
    margin-bottom: 10px;
}

.promotion-popup.show {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px);
}

.promotion-popup::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 8px solid transparent;
    border-top-color: white;
}

.promotion-popup::before {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 9px solid transparent;
    border-top-color: #e1e5e9;
    z-index: -1;
}

.promotion-section {
    margin-bottom: 20px;
}

.promotion-section:last-child {
    margin-bottom: 0;
}

.promotion-section-title {
    font-size: 13px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.promotion-section-icon {
    width: 14px;
    height: 14px;
    color: #667eea;
}

.promotion-link-input {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    font-size: 12px;
    font-family: 'Courier New', monospace;
    background-color: #f8f9fa;
    color: #495057;
    resize: none;
    transition: border-color 0.2s;
    box-sizing: border-box;
}

.promotion-link-input:focus {
    outline: none;
    border-color: #667eea;
    background-color: white;
}

.promotion-qr-container {
    text-align: center;
}

.promotion-qr-code {
    margin: 0 auto 12px;
    padding: 10px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    display: inline-block;
}

.promotion-actions {
    display: flex;
    gap: 8px;
    justify-content: center;
    margin-top: 15px;
}

.promotion-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    font-weight: 500;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 4px;
}

.promotion-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.promotion-btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(102, 126, 234, 0.3);
}

.promotion-btn-secondary {
    background: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;
}

.promotion-btn-secondary:hover {
    background: #e9ecef;
    color: #495057;
}

.promotion-btn-icon {
    width: 12px;
    height: 12px;
}

.copy-success {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 10001;
    animation: copySuccessSlide 3s ease-out forwards;
}

@keyframes copySuccessSlide {
    0% {
        opacity: 0;
        transform: translateX(100%);
    }
    10%, 90% {
        opacity: 1;
        transform: translateX(0);
    }
    100% {
        opacity: 0;
        transform: translateX(100%);
    }
}
</style>

<div class="promotion-link-container">
    <a href="#" class="promotion-link-btn" onclick="togglePromotionPopup(this, '{{ promotion_link|escapejs }}', '{{ product_title|escapejs }}'); return false;">
        <svg class="promotion-link-icon" fill="currentColor" viewBox="0 0 20 20">
            <path d="M12.586 4.586a2 2 0 112.828 2.828l-3 3a2 2 0 01-2.828 0 1 1 0 00-1.414 1.414 4 4 0 005.656 0l3-3a4 4 0 00-5.656-5.656l-1.5 1.5a1 1 0 101.414 1.414l1.5-1.5z"/>
            <path d="M7.414 15.414a2 2 0 01-2.828-2.828l3-3a2 2 0 012.828 0 1 1 0 001.414-1.414 4 4 0 00-5.656 0l-3 3a4 4 0 105.656 5.656l1.5-1.5a1 1 0 10-1.414-1.414l-1.5 1.5z"/>
        </svg>
        推广链接
    </a>

    <!-- 悬浮提示框 -->
    <div class="promotion-popup">
        <div class="promotion-section">
            <div class="promotion-section-title">
                <svg class="promotion-section-icon" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M12.586 4.586a2 2 0 112.828 2.828l-3 3a2 2 0 01-2.828 0 1 1 0 00-1.414 1.414 4 4 0 005.656 0l3-3a4 4 0 00-5.656-5.656l-1.5 1.5a1 1 0 101.414 1.414l1.5-1.5z"/>
                    <path d="M7.414 15.414a2 2 0 01-2.828-2.828l3-3a2 2 0 012.828 0 1 1 0 001.414-1.414 4 4 0 00-5.656 0l-3 3a4 4 0 105.656 5.656l1.5-1.5a1 1 0 10-1.414-1.414l-1.5 1.5z"/>
                </svg>
                推广链接
            </div>
            <textarea class="promotion-link-input promotion-link-text" rows="2" readonly></textarea>
        </div>

        <div class="promotion-section">
            <div class="promotion-section-title">
                <svg class="promotion-section-icon" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm2 2V5h1v1H5zM3 13a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1H4a1 1 0 01-1-1v-3zm2 2v-1h1v1H5zM13 3a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1V4a1 1 0 011-1h3zm-1 2v1h-1V5h1zM11 13a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-3zm2 2v-1h1v1h-1z"/>
                </svg>
                推广二维码
            </div>
            <div class="promotion-qr-container">
                <div class="promotion-qr-code"></div>
                <div class="promotion-actions">
                    <button class="promotion-btn promotion-btn-primary" onclick="copyPromotionLink(this)">
                        <svg class="promotion-btn-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"/>
                            <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"/>
                        </svg>
                        复制链接
                    </button>
                    <button class="promotion-btn promotion-btn-secondary" onclick="downloadQRCode(this)">
                        <svg class="promotion-btn-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"/>
                        </svg>
                        下载二维码
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="{% static 'js/qrcode.min.js' %}"></script>
<script>
let activePopup = null;

function togglePromotionPopup(button, promotionLink, productTitle) {
    const container = button.parentElement;
    const popup = container.querySelector('.promotion-popup');

    // 如果当前弹窗已经显示，则关闭
    if (activePopup === popup && popup.classList.contains('show')) {
        closePromotionPopup();
        return;
    }

    // 关闭其他弹窗
    closePromotionPopup();

    // 设置链接文本
    const linkTextarea = popup.querySelector('.promotion-link-text');
    linkTextarea.value = promotionLink;

    // 清除之前的二维码
    const qrContainer = popup.querySelector('.promotion-qr-code');
    qrContainer.innerHTML = '';

    // 生成新的二维码
    if (promotionLink) {
        new QRCode(qrContainer, {
            text: promotionLink,
            width: 120,
            height: 120,
            colorDark: "#000000",
            colorLight: "#ffffff",
            correctLevel: QRCode.CorrectLevel.M
        });
    }

    // 显示弹窗
    popup.classList.add('show');
    button.classList.add('active');
    activePopup = popup;

    // 存储数据到popup元素上
    popup.promotionLink = promotionLink;
    popup.productTitle = productTitle;
}

function closePromotionPopup() {
    if (activePopup) {
        activePopup.classList.remove('show');
        const button = activePopup.parentElement.querySelector('.promotion-link-btn');
        button.classList.remove('active');
        activePopup = null;
    }
}

function copyPromotionLink(button) {
    const popup = button.closest('.promotion-popup');
    const linkTextarea = popup.querySelector('.promotion-link-text');
    const promotionLink = popup.promotionLink;

    linkTextarea.select();
    linkTextarea.setSelectionRange(0, 99999); // For mobile devices

    try {
        document.execCommand('copy');
        showCopySuccess();
    } catch (err) {
        // Fallback for modern browsers
        if (navigator.clipboard) {
            navigator.clipboard.writeText(promotionLink).then(() => {
                showCopySuccess();
            }).catch(() => {
                alert('复制失败，请手动复制链接');
            });
        } else {
            alert('复制失败，请手动复制链接');
        }
    }
}

function showCopySuccess() {
    const successDiv = document.createElement('div');
    successDiv.className = 'copy-success';
    successDiv.textContent = '✓ 推广链接已复制到剪贴板';
    document.body.appendChild(successDiv);

    setTimeout(() => {
        if (document.body.contains(successDiv)) {
            document.body.removeChild(successDiv);
        }
    }, 3000);
}

function downloadQRCode(button) {
    const popup = button.closest('.promotion-popup');
    const canvas = popup.querySelector('.promotion-qr-code canvas');
    const productTitle = popup.productTitle;

    if (canvas) {
        const link = document.createElement('a');
        link.download = `推广二维码-${productTitle || '商品'}.png`;
        link.href = canvas.toDataURL();
        link.click();
    }
}

// 点击外部关闭弹窗
document.addEventListener('click', function(event) {
    if (activePopup && !activePopup.parentElement.contains(event.target)) {
        closePromotionPopup();
    }
});

// ESC键关闭弹窗
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closePromotionPopup();
    }
});
</script>
